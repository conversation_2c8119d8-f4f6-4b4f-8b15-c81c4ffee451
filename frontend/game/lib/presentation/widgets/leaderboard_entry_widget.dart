import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/leaderboard_entity.dart';
import '../../domain/entities/font_entity.dart';
import '../../core/constants/app_constants.dart';
import '../providers/theme_providers.dart';
import '../providers/font_providers.dart';
import 'board_snapshot_widget.dart';

/// Widget for displaying a single leaderboard entry
class LeaderboardEntryWidget extends ConsumerWidget {
  final LeaderboardEntry entry;
  final int rank;
  final bool isHighlighted;
  final VoidCallback? onTap;

  const LeaderboardEntryWidget({
    super.key,
    required this.entry,
    required this.rank,
    this.isHighlighted = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPrimaryColor = ref.watch(currentPrimaryColorProvider);
    final currentFont = ref.watch(currentFontProvider);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Material(
        elevation: isHighlighted ? 8 : 2,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadiusMedium,
              ),
              border: isHighlighted
                  ? Border.all(color: currentPrimaryColor, width: 2)
                  : null,
              gradient: isHighlighted
                  ? LinearGradient(
                      colors: [
                        currentPrimaryColor.withValues(alpha: 0.1),
                        currentPrimaryColor.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : null,
            ),
            child: Row(
              children: [
                // Rank badge
                _buildRankBadge(
                  rank,
                  currentPrimaryColor,
                  currentFont,
                  isDarkMode,
                ),

                const SizedBox(width: AppConstants.paddingMedium),

                // Board snapshot
                BoardSnapshotPreview(
                  boardSnapshot: entry.boardSnapshot,
                  size: 70,
                  onTap: onTap,
                ),

                const SizedBox(width: AppConstants.paddingMedium),

                // Game details
                Expanded(
                  child: _buildGameDetails(
                    entry,
                    currentFont,
                    currentPrimaryColor,
                    isDarkMode,
                  ),
                ),

                const SizedBox(width: AppConstants.paddingSmall),

                // Score and metadata
                _buildScoreSection(
                  entry,
                  currentFont,
                  currentPrimaryColor,
                  isDarkMode,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRankBadge(
    int rank,
    Color primaryColor,
    FontEntity? currentFont,
    bool isDarkMode,
  ) {
    Color badgeColor;
    Color textColor;
    IconData? icon;

    switch (rank) {
      case 1:
        badgeColor = const Color(0xFFFFD700); // Gold
        textColor = Colors.black;
        icon = Icons.emoji_events;
        break;
      case 2:
        badgeColor = const Color(0xFFC0C0C0); // Silver
        textColor = Colors.black;
        icon = Icons.emoji_events;
        break;
      case 3:
        badgeColor = const Color(0xFFCD7F32); // Bronze
        textColor = Colors.white;
        icon = Icons.emoji_events;
        break;
      default:
        badgeColor = primaryColor;
        textColor = Colors.white;
        break;
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: badgeColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: icon != null
            ? Icon(icon, color: textColor, size: 20)
            : Text(
                rank.toString(),
                style: TextStyle(
                  color: textColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily: currentFont?.fontFamily,
                ),
              ),
      ),
    );
  }

  Widget _buildGameDetails(
    LeaderboardEntry entry,
    FontEntity? currentFont,
    Color primaryColor,
    bool isDarkMode,
  ) {
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final subtitleColor = isDarkMode ? Colors.white70 : Colors.black54;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Game mode
        Text(
          entry.detailedGameMode,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: textColor,
            fontFamily: currentFont?.fontFamily,
          ),
        ),

        const SizedBox(height: 4),

        // Duration
        Row(
          children: [
            Icon(Icons.timer, size: 14, color: subtitleColor),
            const SizedBox(width: 4),
            Text(
              entry.formattedDuration,
              style: TextStyle(
                fontSize: 12,
                color: subtitleColor,
                fontFamily: currentFont?.fontFamily,
              ),
            ),
          ],
        ),

        const SizedBox(height: 2),

        // Date
        Row(
          children: [
            Icon(Icons.calendar_today, size: 14, color: subtitleColor),
            const SizedBox(width: 4),
            Text(
              entry.relativeDateString,
              style: TextStyle(
                fontSize: 12,
                color: subtitleColor,
                fontFamily: currentFont?.fontFamily,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScoreSection(
    LeaderboardEntry entry,
    FontEntity? currentFont,
    Color primaryColor,
    bool isDarkMode,
  ) {
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Score
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: primaryColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            entry.formattedScore,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: primaryColor,
              fontFamily: currentFont?.fontFamily,
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Absolute date (smaller)
        Text(
          entry.absoluteDateString,
          style: TextStyle(
            fontSize: 10,
            color: textColor.withValues(alpha: 0.6),
            fontFamily: currentFont?.fontFamily,
          ),
        ),
      ],
    );
  }
}

/// Compact version of leaderboard entry for smaller displays
class CompactLeaderboardEntryWidget extends ConsumerWidget {
  final LeaderboardEntry entry;
  final int rank;
  final VoidCallback? onTap;

  const CompactLeaderboardEntryWidget({
    super.key,
    required this.entry,
    required this.rank,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPrimaryColor = ref.watch(currentPrimaryColorProvider);
    final currentFont = ref.watch(currentFontProvider);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black87;

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: 4,
      ),
      child: Material(
        elevation: 1,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingSmall),
            child: Row(
              children: [
                // Rank
                SizedBox(
                  width: 30,
                  child: Text(
                    '#$rank',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: currentPrimaryColor,
                      fontFamily: currentFont?.fontFamily,
                    ),
                  ),
                ),

                // Compact board snapshot
                CompactBoardSnapshotWidget(
                  boardSnapshot: entry.boardSnapshot,
                  size: 40,
                ),

                const SizedBox(width: AppConstants.paddingSmall),

                // Game info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        entry.gameMode,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: textColor,
                          fontFamily: currentFont?.fontFamily,
                        ),
                      ),
                      Text(
                        entry.relativeDateString,
                        style: TextStyle(
                          fontSize: 10,
                          color: textColor.withValues(alpha: 0.6),
                          fontFamily: currentFont?.fontFamily,
                        ),
                      ),
                    ],
                  ),
                ),

                // Score
                Text(
                  entry.formattedScore,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: currentPrimaryColor,
                    fontFamily: currentFont?.fontFamily,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
