import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/localization/localization_manager.dart';
import '../../domain/entities/font_entity.dart';
import '../providers/theme_providers.dart';
import '../providers/font_providers.dart';
import '../providers/leaderboard_providers.dart';
import '../widgets/leaderboard_entry_widget.dart';

/// Screen displaying the local leaderboard
class LeaderboardScreen extends ConsumerStatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  ConsumerState<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends ConsumerState<LeaderboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentPrimaryColor = ref.watch(currentPrimaryColorProvider);
    final currentFont = ref.watch(currentFontProvider);
    final leaderboard = ref.watch(filteredLeaderboardProvider);
    final selectedGameMode = ref.watch(selectedGameModeFilterProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          LocalizationManager.leaderboard(ref),
          style: TextStyle(fontFamily: currentFont?.fontFamily),
        ),
        elevation: 0,
        actions: [
          // Filter button
          PopupMenuButton<String?>(
            icon: Icon(Icons.filter_list, color: currentPrimaryColor),
            onSelected: (gameMode) {
              ref.read(selectedGameModeFilterProvider.notifier).state =
                  gameMode;
            },
            itemBuilder: (context) {
              final availableGameModes = ref
                  .read(leaderboardProvider.notifier)
                  .getAvailableGameModes();

              return [
                PopupMenuItem<String?>(
                  value: null,
                  child: Text(
                    LocalizationManager.allGameModes(ref),
                    style: TextStyle(fontFamily: currentFont?.fontFamily),
                  ),
                ),
                ...availableGameModes.map((gameMode) {
                  return PopupMenuItem<String>(
                    value: gameMode,
                    child: Text(
                      gameMode,
                      style: TextStyle(fontFamily: currentFont?.fontFamily),
                    ),
                  );
                }),
              ];
            },
          ),

          // Refresh button
          IconButton(
            icon: Icon(Icons.refresh, color: currentPrimaryColor),
            onPressed: () {
              ref.read(leaderboardProvider.notifier).refresh();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: currentPrimaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: currentPrimaryColor,
          tabs: [
            Tab(
              text: LocalizationManager.leaderboard(ref),
              icon: const Icon(Icons.leaderboard),
            ),
            Tab(
              text: LocalizationManager.statistics(ref),
              icon: const Icon(Icons.bar_chart),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildLeaderboardTab(leaderboard, selectedGameMode, currentFont),
          _buildStatisticsTab(currentFont, currentPrimaryColor),
        ],
      ),
    );
  }

  Widget _buildLeaderboardTab(
    AsyncValue leaderboard,
    String? selectedGameMode,
    FontEntity? currentFont,
  ) {
    return Column(
      children: [
        // Filter indicator
        if (selectedGameMode != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.paddingSmall),
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            child: Row(
              children: [
                Icon(
                  Icons.filter_list,
                  size: 16,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  '${LocalizationManager.filtering(ref)}: $selectedGameMode',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).primaryColor,
                    fontFamily: currentFont?.fontFamily,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    ref.read(selectedGameModeFilterProvider.notifier).state =
                        null;
                  },
                  child: Text(
                    LocalizationManager.clearFilter(ref),
                    style: TextStyle(fontFamily: currentFont?.fontFamily),
                  ),
                ),
              ],
            ),
          ),

        // Leaderboard content
        Expanded(
          child: leaderboard.when(
            data: (entries) {
              if (entries.isEmpty) {
                return _buildEmptyState(currentFont);
              }

              return RefreshIndicator(
                onRefresh: () async {
                  await ref.read(leaderboardProvider.notifier).refresh();
                },
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.paddingMedium,
                  ),
                  itemCount: entries.length,
                  itemBuilder: (context, index) {
                    final entry = entries[index];
                    final rank = index + 1;

                    return LeaderboardEntryWidget(
                      entry: entry,
                      rank: rank,
                      isHighlighted: rank <= 3,
                      onTap: () => _showEntryDetails(entry, rank),
                    );
                  },
                ),
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) => _buildErrorState(error, currentFont),
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsTab(FontEntity? currentFont, Color primaryColor) {
    final stats = ref.read(leaderboardProvider.notifier).getLeaderboardStats();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocalizationManager.leaderboardStatistics(ref),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              fontFamily: currentFont?.fontFamily,
            ),
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          _buildStatCard(
            title: LocalizationManager.totalEntries(ref),
            value: stats['totalEntries'].toString(),
            icon: Icons.list,
            color: primaryColor,
            fontFamily: currentFont?.fontFamily,
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          _buildStatCard(
            title: LocalizationManager.highestScore(ref),
            value: _formatScore(stats['highestScore']),
            icon: Icons.emoji_events,
            color: const Color(0xFFFFD700),
            fontFamily: currentFont?.fontFamily,
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          _buildStatCard(
            title: LocalizationManager.averageScore(ref),
            value: _formatScore(stats['averageScore']),
            icon: Icons.trending_up,
            color: Colors.blue,
            fontFamily: currentFont?.fontFamily,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? fontFamily,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),

            const SizedBox(width: AppConstants.paddingMedium),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontFamily: fontFamily,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: color,
                      fontFamily: fontFamily,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(FontEntity? currentFont) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.leaderboard, size: 64, color: Colors.grey[400]),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            LocalizationManager.noLeaderboardEntries(ref),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontFamily: currentFont?.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            LocalizationManager.playGamesToSeeLeaderboard(ref),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
              fontFamily: currentFont?.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error, FontEntity? currentFont) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            LocalizationManager.errorLoadingLeaderboard(ref),
            style: TextStyle(
              fontSize: 18,
              color: Colors.red[600],
              fontFamily: currentFont?.fontFamily,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ElevatedButton(
            onPressed: () {
              ref.read(leaderboardProvider.notifier).refresh();
            },
            child: Text(
              LocalizationManager.retry(ref),
              style: TextStyle(fontFamily: currentFont?.fontFamily),
            ),
          ),
        ],
      ),
    );
  }

  void _showEntryDetails(entry, int rank) {
    // TODO: Implement detailed entry view
    // This could show a larger board snapshot, detailed stats, etc.
  }

  String _formatScore(int score) {
    if (score >= 10000) {
      return '${(score / 1000).round()}k';
    }
    return score.toString();
  }
}
