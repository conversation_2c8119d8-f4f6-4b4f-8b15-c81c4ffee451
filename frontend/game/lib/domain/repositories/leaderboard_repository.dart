import '../entities/leaderboard_entity.dart';

/// Repository interface for leaderboard operations
/// Following clean architecture principles - domain layer defines contracts
abstract class LeaderboardRepository {
  /// Get all leaderboard entries sorted by score (highest first)
  Future<List<LeaderboardEntry>> getLeaderboard();

  /// Add a new leaderboard entry
  /// Automatically maintains top 10 entries and removes lower scores
  Future<void> addLeaderboardEntry(LeaderboardEntry entry);

  /// Check if a score qualifies for the leaderboard
  Future<bool> isScoreEligible(int score);

  /// Get the lowest score currently in the leaderboard
  Future<int?> getLowestLeaderboardScore();

  /// Clear all leaderboard entries
  Future<void> clearLeaderboard();

  /// Get leaderboard entries filtered by game mode
  Future<List<LeaderboardEntry>> getLeaderboardByGameMode(String gameMode);

  /// Get the rank of a specific score (1-based ranking)
  Future<int?> getScoreRank(int score);

  /// Check if leaderboard is empty
  Future<bool> isLeaderboardEmpty();

  /// Get total number of entries in leaderboard
  Future<int> getLeaderboardCount();
}
